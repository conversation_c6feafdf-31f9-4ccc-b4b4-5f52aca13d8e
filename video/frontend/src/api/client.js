import axios from 'axios'
import { useAppStore } from '@/stores/app'

// 创建axios实例
const client = axios.create({
  baseURL: '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
client.interceptors.request.use(
  (config) => {
    // 显示加载状态
    const appStore = useAppStore()
    appStore.setLoading(true)

    // 添加认证token
    const token = localStorage.getItem('access_token')
    console.log(`API 请求 ${config.url}:`, {
      token: token ? `${token.substring(0, 20)}...` : 'null',
      hasAuthHeader: !!config.headers.Authorization
    })

    if (token) {
      config.headers.Authorization = `Bearer ${token}`
      console.log('已添加 Authorization 头')
    } else {
      console.log('没有 token，跳过 Authorization 头')
    }

    return config
  },
  (error) => {
    const appStore = useAppStore()
    appStore.setLoading(false)
    return Promise.reject(error)
  }
)

// 响应拦截器
client.interceptors.response.use(
  (response) => {
    // 隐藏加载状态
    const appStore = useAppStore()
    appStore.setLoading(false)

    return response.data
  },
  (error) => {
    const appStore = useAppStore()
    appStore.setLoading(false)

    // 处理错误
    let errorMessage = '请求失败'

    if (error.response) {
      // 服务器返回错误状态码
      const { status, data } = error.response

      switch (status) {
        case 400:
          errorMessage = data.detail || '请求参数错误'
          break
        case 401:
          errorMessage = '未授权，请重新登录'
          // 清除token并跳转到登录页
          localStorage.removeItem('access_token')
          window.location.href = '/login'
          break
        case 403:
          errorMessage = '权限不足'
          break
        case 404:
          errorMessage = '资源不存在'
          break
        case 422:
          errorMessage = data.detail || '数据验证失败'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        default:
          errorMessage = data.detail || `请求失败 (${status})`
      }
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络设置'
    } else {
      // 其他错误
      errorMessage = error.message || '未知错误'
    }

    // 显示错误通知
    appStore.showError('请求失败', errorMessage)

    return Promise.reject(error)
  }
)

export default client
